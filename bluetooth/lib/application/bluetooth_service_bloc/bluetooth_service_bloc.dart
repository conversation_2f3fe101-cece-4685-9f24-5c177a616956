import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/model/device_model.dart';
import '../../../domain/failure/bluetooth_failure.dart';
import '../../../domain/facade/bluetooth_facade.dart';
part 'bluetooth_service_bloc.freezed.dart';
part 'bluetooth_service_event.dart';
part 'bluetooth_service_state.dart';

@injectable
class BluetoothServiceBloc
    extends Bloc<BluetoothServiceEvent, BluetoothServiceState> {
  final IBluetoothFacade _bluetoothFacade;
  StreamSubscription<BluetoothConnectionState>? _deviceStateSubscription;
  StreamSubscription<Either<BluetoothFailure, List<DeviceModel?>>>?
      _streamSavedDevicesSubscription;

  // Connection retry parameters
  static const int _maxRetries = 2;

  // Debounce mechanism for refresh
  DateTime? _lastRefreshTime;

  BluetoothServiceBloc(this._bluetoothFacade)
      : super(const BluetoothServiceState.initial()) {
    on<CheckBluetooth>(_onCheckBluetooth);
    on<CheckRecentDevice>(_onCheckRecentDevice);
    on<StartSearch>(_onStartSearch);
    on<ConnectToDevice>(_onConnectToDevice);
    on<DeviceDisconnected>(_onDeviceDisconnected);
    on<DisconnectDevice>(_onDisconnectDevice);
    on<ListenToDevice>(_onListenToDevice);
    on<CheckConnection>(_onCheckConnection);
    on<SavedDevicesReceived>(_onSavedDevicesReceived);
    on<ReconnectDevice>(_onReconnectDevice);
    on<MoveToLandingPage>(_onMoveToLandingPage);
    on<RefreshSavedDevices>(_onRefreshSavedDevices);
  }

  /// Handles checking if Bluetooth is enabled
  Future<void> _onCheckBluetooth(
      CheckBluetooth event, Emitter<BluetoothServiceState> emit) async {
    await for (final isBluetoothOn in _bluetoothFacade.isBluetoothOn()) {
      if (isBluetoothOn) {
        emit(const BluetoothServiceState.bluetoothOn());
        add(const BluetoothServiceEvent.checkRecentDevice());
      } else {
        emit(const BluetoothServiceState.bluetoothOff());
        await _streamSavedDevicesSubscription?.cancel();
        await _deviceStateSubscription?.cancel();
      }
    }
  }

  /// Checks for the most recently connected device
  Future<void> _onCheckRecentDevice(
      CheckRecentDevice event, Emitter<BluetoothServiceState> emit) async {
    emit(const BluetoothServiceState.checkingRecentDevice());

    final connectedDeviceResult = await _bluetoothFacade.deviceConnected();
    await connectedDeviceResult.mapBoth(
      onLeft: (failure) async {
        // No device currently connected, check for recent device
        final recentDeviceResult = await _bluetoothFacade.getRecentDevice();
        await recentDeviceResult.mapBoth(
          onLeft: (failure) {
            // No recent device, start fetching saved devices
            _startFetchingSavedDevices();
          },
          onRight: (device) {
            // Recent device found, try to connect
            add(ConnectToDevice(device.deviceInfo.deviceId.toString(),
                initial: false));
          },
        );
      },
      onRight: (device) {
        if (device != null) {
          // Device already connected
          emit(BluetoothServiceState.connected(device));
          add(ListenToDevice(device));
        } else {
          _startFetchingSavedDevices();
        }
      },
    );
  }

  /// Start fetching saved devices process
  void _startFetchingSavedDevices() {
    emit(const BluetoothServiceState.landingPageState());
    // Cancel existing subscription
    _streamSavedDevicesSubscription?.cancel();

    // Set up a new subscription and handle updates through a dedicated event
    _streamSavedDevicesSubscription =
        _bluetoothFacade.getSavedDevicesList().listen(
      (failureOrDevices) {
        add(BluetoothServiceEvent.savedDevicesReceived(failureOrDevices));
      },
    );
  }

  /// Handle saved devices updates from stream
  Future<void> _onSavedDevicesReceived(
      SavedDevicesReceived event, Emitter<BluetoothServiceState> emit) async {
    event.failureOrDevices.mapBoth(
      onRight: (devices) {
        if (devices.isNotEmpty) {
          emit(BluetoothServiceState.savedDevices(
            devices,
            version: state.maybeMap(
              savedDevices: (s) => s.version + 1,
              orElse: () => 1,
            ),
          ));
        } else {
          _streamSavedDevicesSubscription?.cancel();
          emit(const BluetoothServiceState.landingPageState());
        }
      },
      onLeft: (failure) {
        print('Error fetching saved devices: $failure');
        emit(BluetoothServiceState.bluetoothError(failure));
        add(const StartSearch(true));
      },
    );
  }

  /// Starts scanning for nearby Bluetooth devices
  Future<void> _onStartSearch(
      StartSearch event, Emitter<BluetoothServiceState> emit) async {
    // Stop background scanning when starting active search
    _streamSavedDevicesSubscription?.cancel();
    _bluetoothFacade.stopBackgroundScanning();

    emit(const BluetoothServiceState.bluetoothSearching());
    final result = await _bluetoothFacade.searchForDevices();
    result.mapBoth(
      onLeft: (failure) => emit(BluetoothServiceState.bluetoothError(failure)),
      onRight: (devices) {
        if (devices != null && devices.isNotEmpty) {
          emit(BluetoothServiceState.bluetoothAvailableTypeDevices(devices));
        } else {
          emit(const BluetoothServiceState.bluetoothError(
              BluetoothFailure.noDevicesFound()));
        }
      },
    );
  }

  /// Handles connecting to a specific device
  Future<void> _onConnectToDevice(
      ConnectToDevice event, Emitter<BluetoothServiceState> emit) async {
    emit(const BluetoothServiceState.connecting());

    final result = await _bluetoothFacade.pairDevice(
        event.deviceId, event.initial != true ? false : true);
    await result.mapBoth(
      onLeft: (failure) async {
        if (event.initial == false) {
          print('initial connection failed.. fetching saved devices');
          // If connection failed but not an initial connection, show saved devices
          _startFetchingSavedDevices();
        } else {
          if (!emit.isDone) {
            emit(BluetoothServiceState.bluetoothError(failure));
          }
        }
      },
      onRight: (device) async {
        // Cancel saved devices subscription when connection is successful
        _streamSavedDevicesSubscription?.cancel();

        if (event.initial == true) {
          if (!emit.isDone) {
            emit(BluetoothServiceState.connectionIntro(device));
          }
        } else {
          add(ListenToDevice(device));
        }
      },
    );
  }

  /// Handles disconnecting from a device
  Future<void> _onDisconnectDevice(
      DisconnectDevice event, Emitter<BluetoothServiceState> emit) async {
    try {
      await event.device.disconnect();
      emit(BluetoothServiceState.disconnected(event.device));
      _startFetchingSavedDevices();
    } catch (e) {
      emit(const BluetoothServiceState.bluetoothError(
          BluetoothFailure.disconnectionFailed()));
    }
  }

  /// Handles device disconnection events
  Future<void> _onDeviceDisconnected(
      DeviceDisconnected event, Emitter<BluetoothServiceState> emit) async {
    if (_deviceStateSubscription != null) {
      event.device.cancelWhenDisconnected(
        _deviceStateSubscription!,
        delayed: false,
        next: true,
      );
    }
    emit(BluetoothServiceState.disconnected(event.device));
    _startFetchingSavedDevices();
  }

  /// Sets up a subscription to listen for device state changes
  Future<void> _onListenToDevice(
      ListenToDevice event, Emitter<BluetoothServiceState> emit) async {
    await _deviceStateSubscription?.cancel();
    // Cancel saved devices subscription when device is connected
    _streamSavedDevicesSubscription?.cancel();
    emit(BluetoothServiceState.connected(event.device));
    _deviceStateSubscription =
        _bluetoothFacade.listenToDevice(event.device).listen(
      (deviceState) async {
        if (deviceState == BluetoothConnectionState.connected) {
          if (state is! Connected) {
            if (!emit.isDone) {
              emit(BluetoothServiceState.connected(event.device));
            }
          }
        }

        if (deviceState == BluetoothConnectionState.disconnected) {
          // When disconnected, attempt reconnection through dedicated event
          add(ReconnectDevice(event.device));
        }
      },
      onError: (e) async {
        if (!emit.isDone) {
          emit(const BluetoothServiceState.bluetoothError(
              BluetoothFailure.deviceConnectionLost()));
        }
        _startFetchingSavedDevices();
      },
    );
  }

  /// Handles device reconnection events with multiple retries
  Future<void> _onReconnectDevice(
      ReconnectDevice event, Emitter<BluetoothServiceState> emit) async {
    const int maxReconnectRetries = 3;
    await _deviceStateSubscription?.cancel();
    emit(BluetoothServiceState.reconnecting(event.device));

    for (int retryCount = 0; retryCount < maxReconnectRetries; retryCount++) {
      final result = await _bluetoothFacade.reconnectToDevice(event.device);
      print('!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!reconnect result: $result');

      bool success = false;
      result.map((device) {
        emit(BluetoothServiceState.connected(device));
        add(ListenToDevice(device));
        success = true;
      });

      if (success) {
        return; // Successfully reconnected, exit retry loop
      }

      // Add delay between retries
      if (retryCount < maxReconnectRetries - 1) {
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    // All retries failed
    emit(BluetoothServiceState.disconnected(event.device));
    _startFetchingSavedDevices();
  }

  /// Checks the current connection state
  Future<void> _onCheckConnection(
      CheckConnection event, Emitter<BluetoothServiceState> emit) async {
    final result = await _bluetoothFacade.deviceConnected();
    result.mapBoth(
      onLeft: (failure) => _startFetchingSavedDevices(),
      onRight: (device) {
        if (device != null) {
          emit(BluetoothServiceState.connected(device));
          add(ListenToDevice(device));
        } else {
          _startFetchingSavedDevices();
        }
      },
    );
  }

  /// Handles moving to the landing page
  Future<void> _onMoveToLandingPage(
      MoveToLandingPage event, Emitter<BluetoothServiceState> emit) async {
    await _streamSavedDevicesSubscription?.cancel();
    await _deviceStateSubscription?.cancel();
    emit(const BluetoothServiceState.landingPageState());
    _startFetchingSavedDevices();
  }

  /// Handles refreshing saved devices availability
  Future<void> _onRefreshSavedDevices(
      RefreshSavedDevices event, Emitter<BluetoothServiceState> emit) async {
    // Don't refresh if there's an active connection or connection process
    if (state is Connected ||
        state is Connecting ||
        state is ConnectionIntro ||
        state is Reconnecting) {
      return;
    }

    // Debounce: Don't refresh if last refresh was less than 2 seconds ago
    final now = DateTime.now();
    if (_lastRefreshTime != null &&
        now.difference(_lastRefreshTime!).inSeconds < 2) {
      return;
    }
    _lastRefreshTime = now;

    final result = await _bluetoothFacade.refreshSavedDevices();
    result.mapBoth(
      onLeft: (failure) => emit(BluetoothServiceState.bluetoothError(failure)),
      onRight: (devices) {
        if (devices.isNotEmpty) {
          emit(BluetoothServiceState.savedDevices(
            devices,
            version: state.maybeMap(
              savedDevices: (s) => s.version + 1,
              orElse: () => 1,
            ),
          ));
        } else {
          emit(const BluetoothServiceState.landingPageState());
        }
      },
    );
  }

  @override
  Future<void> close() async {
    await _deviceStateSubscription?.cancel();
    await _streamSavedDevicesSubscription?.cancel();
    return super.close();
  }
}

import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart';
import 'package:remote/domain/model/device_model.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_route/auto_route.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../routing/app_pages.gr.dart';

//landing page for saved devices, connecting new device and virtual experience
class LandingPage extends StatefulWidget {
  const LandingPage({Key? key}) : super(key: key);

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  @override
  void initState() {
    super.initState();
    // Refresh saved devices when landing page is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentState = context.read<BluetoothServiceBloc>().state;
      // Only refresh if not in a connection state
      if (currentState is! Connected &&
          currentState is! Connecting &&
          currentState is! ConnectionIntro &&
          currentState is! Reconnecting) {
        context.read<BluetoothServiceBloc>().add(
              const BluetoothServiceEvent.refreshSavedDevices(),
            );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        height: .35.sw, // The height of your curved app bar
      ),
      body: BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
        builder: (context, state) {
          return RefreshIndicator(
              onRefresh: () async {
                context.read<BluetoothServiceBloc>().add(
                      const BluetoothServiceEvent.refreshSavedDevices(),
                    );
                // Wait a bit for the refresh to complete
                await Future.delayed(const Duration(milliseconds: 500));
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: 16.0, horizontal: 25.0),
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with My Devices title and + button
                      Row(
                        children: [
                          Text(
                            'My Devices',
                            style:
                                Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      color: Color(0xff26204a),
                                      fontWeight: FontWeight.w600,
                                      fontSize: 20,
                                    ),
                          ),
                          Spacer(),
                          IconButton(
                            icon: Icon(
                              Icons.add,
                              color: Color(0xff26204a),
                              size: 30,
                            ),
                            onPressed: () {
                              context
                                  .read<BluetoothServiceBloc>()
                                  .add(const StartSearch(false));
                            },
                          ),
                        ],
                      ),
                      SizedBox(height: 20),

                      // Show saved devices or no devices message
                      _buildDevicesSection(context, state),

                      SizedBox(height: 20),
                      Container(
                        width: 1.sw,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: Color(0xffFAF2DF)),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            children: [
                              SizedBox(height: 10),
                              Row(
                                children: [
                                  Container(
                                    width: .6.sw,
                                    child: RichText(
                                      text: TextSpan(
                                        text: 'Virtual Experience',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              color: Color(0xff26204a),
                                              fontWeight: FontWeight.w600,
                                              fontSize: 20,
                                            ),
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: '\n',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                                  color: AppTheme.primaryColor,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                          ),
                                          TextSpan(
                                            text:
                                                'Experience the device virtually, learn how to use it, and explore its features.',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                                  color: Colors.grey,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Spacer(),
                                  Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: AppTheme.primaryColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: IconButton(
                                      icon: Icon(Icons.arrow_forward_rounded,
                                          color: Colors.white, size: 30),
                                      onPressed: () {
                                        // Navigate to virtual remote page
                                        context.router
                                            .push(VirtualRemoteRoute());
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20),
                              Image.asset(
                                'assets/product_showcase/juno_phone.png',
                                width: 1.sw,
                                fit: BoxFit.fitWidth,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ));
        },
      ),
    );
  }

  /// Build the devices section based on the current state
  Widget _buildDevicesSection(
      BuildContext context, BluetoothServiceState state) {
    return state.maybeWhen(
      savedDevices: (devices, version) {
        if (devices.isNotEmpty) {
          return _buildSavedDevicesList(devices);
        } else {
          return _buildNoDevicesMessage(context);
        }
      },
      landingPageState: () => _buildNoDevicesMessage(context),
      orElse: () => _buildNoDevicesMessage(context),
    );
  }

  /// Build horizontal list of saved devices
  Widget _buildSavedDevicesList(List<DeviceModel> devices) {
    return Container(
      height: 140,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: devices.length,
        itemBuilder: (context, index) {
          final device = devices[index];
          return Container(
            width: 200,
            margin: EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Image.asset(
                        'assets/remote/device_pair.png',
                        height: 50,
                        width: 80,
                        fit: BoxFit.fitWidth,
                      ),
                      Expanded(
                        child: Text(
                          'Juno Lilly',
                          style: GoogleFonts.poppins(
                            fontSize: 15,
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  GestureDetector(
                    onTap: () {
                      if (device.isDeviceReady == true) {
                        context.read<BluetoothServiceBloc>().add(
                            ConnectToDevice(device.deviceInfo.deviceId!,
                                initial: true));
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: device.isDeviceReady == true
                              ? AppTheme.primaryColor
                              : Colors.grey,
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Center(
                          child: Text(
                            device.isDeviceReady == true
                                ? "Tap to Connect"
                                : "Not Available",
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build no devices message with pair device button
  Widget _buildNoDevicesMessage(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.0),
      height: .45.sw,
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: [
            RichText(
              text: TextSpan(
                text: 'No devices found. ',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Color(0xff26204a),
                      fontWeight: FontWeight.w400,
                    ),
                children: <TextSpan>[
                  TextSpan(
                    text: 'Connect a new device to get started.',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w400,
                        ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              onPressed: () {
                context
                    .read<BluetoothServiceBloc>()
                    .add(const StartSearch(false));
              },
              child: Text('Pair Device', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:async';

import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

/// Manages Bluetooth device connection monitoring
///
/// Handles device connection and disconnection events
class ConnectionManager {
  final IBluetoothFacade _bluetoothFacade;

  // Connection monitoring
  StreamSubscription? _deviceConnectionSubscription;
  StreamSubscription? _deviceInfoSubscription;

  // Callbacks for connection events
  final Function() _onDeviceConnected;
  final Function() _onDeviceDisconnected;

  // Track connected device to prevent duplicate notifications
  String? _connectedDeviceId;
  bool _isSettingUpConnection = false;

  // Debouncing to prevent rapid reconnection attempts
  DateTime? _lastDisconnectionTime;
  static const Duration _reconnectionDebounceDelay = Duration(seconds: 5);

  ConnectionManager({
    required IBluetoothFacade bluetoothFacade,
    required Function() onDeviceConnected,
    required Function() onDeviceDisconnected,
  })  : _bluetoothFacade = bluetoothFacade,
        _onDeviceConnected = onDeviceConnected,
        _onDeviceDisconnected = onDeviceDisconnected {
    _initConnectionMonitoring();
  }

  /// Initialize device connection monitoring
  Future<void> _initConnectionMonitoring() async {
    debugPrint("Initializing device connection monitoring");

    // Get the currently connected device
    final deviceResult = await _bluetoothFacade.getConnectedDevice();

    deviceResult.mapBoth(onLeft: (failure) {
      // No device connected or error, nothing to monitor yet
      debugPrint('No device connected to monitor: $failure');
    }, onRight: (device) {
      // Start monitoring this device's connection state
      _setupConnectionMonitoring(device);
    });

    // Don't automatically set up monitoring based on device info changes
    // This was causing the infinite loop when device is offline
    // Only set up monitoring when explicitly connected to a device
  }

  /// Set up monitoring for a specific device
  void _setupConnectionMonitoring(BluetoothDevice device) {
    if (_connectedDeviceId == device.remoteId.str &&
        _deviceConnectionSubscription != null) {
      // Already monitoring this device with active subscription
      return;
    }

    _isSettingUpConnection = true;

    try {
      // Cancel any existing subscription
      _deviceConnectionSubscription?.cancel();

      // Store the connected device ID
      _connectedDeviceId = device.remoteId.str;
      debugPrint("Setting up monitoring for device: ${device.remoteId.str}");

      // Monitor connection state changes with error handling
      _deviceConnectionSubscription =
          _bluetoothFacade.listenToDevice(device).listen(
        (connectionState) {
          if (connectionState == BluetoothConnectionState.disconnected) {
            debugPrint("Device disconnected: ${device.remoteId}");
            // Clean up immediately to prevent re-monitoring
            _connectedDeviceId = null;
            _deviceConnectionSubscription?.cancel();
            _deviceConnectionSubscription = null;
            _onDeviceDisconnected();
          } else if (connectionState == BluetoothConnectionState.connected) {
            debugPrint("Device connected: ${device.remoteId}");
            // Only trigger once per connection
            if (_connectedDeviceId == device.remoteId.str) {
              _onDeviceConnected();
            }
          }
        },
        onError: (error) {
          debugPrint("Connection monitoring error: $error");
          // Clean up on error to prevent loops
          _connectedDeviceId = null;
          _deviceConnectionSubscription?.cancel();
          _deviceConnectionSubscription = null;
        },
        cancelOnError: false,
      );
    } finally {
      _isSettingUpConnection = false;
    }
  }

  /// Check if a device is currently connected
  bool get isDeviceConnected => _connectedDeviceId != null;

  /// Get the ID of the connected device
  String? get connectedDeviceId => _connectedDeviceId;

  /// Stop monitoring the current device
  void stopMonitoring() {
    debugPrint("Stopping device connection monitoring");
    _deviceConnectionSubscription?.cancel();
    _deviceConnectionSubscription = null;
    _connectedDeviceId = null;
  }

  /// Clean up resources
  void dispose() {
    debugPrint("Disposing connection manager");
    _deviceConnectionSubscription?.cancel();
    _deviceConnectionSubscription = null;
    _deviceInfoSubscription?.cancel();
    _deviceInfoSubscription = null;
    _connectedDeviceId = null;
  }
}
